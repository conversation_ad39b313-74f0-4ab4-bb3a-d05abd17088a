import QuestionnaireSection from '@/features/questionnaire/components/layouts/QuestionnaireSection';
import type { QuestionnaireSectionTypeRef } from '@/features/questionnaire/components/layouts/QuestionnaireSection';
import type { QuestionInSectionExtend, QuestionnairePayload, QuestionnaireResponse } from '@/features/questionnaire/types/questionnaire';
import { useImperativeHandle, useRef } from 'react';

type QuantityQuestionnaireType = {
  questionnaire: QuestionnaireResponse | null;
  ref?: React.Ref<QuestionnaireSectionTypeRef>;
};

const QuantityQuestionnaire: React.FC<QuantityQuestionnaireType> = ({ questionnaire, ref }) => {
  const formRef = useRef<QuestionnaireSectionTypeRef[]>([]);

  useImperativeHandle(ref, () => {
    return {
      onSubmitForm: () => {
        const payload = ((questionnaire?.sections ?? []).map((section, index) => ({
          ...section,
          questions: (formRef.current[index]?.onSubmitForm() ?? []) as QuestionInSectionExtend[],
        }))) as QuestionnairePayload[];

        return payload;
      },
    };
  }, []);

  return (
    <div className="mt-2">
      {/* {questionnaire && questionnaire.introduction && (
        <div className="bg-white shadow-xl rounded-2xl p-4 mb-4">
          <QuestionnaireMarkdown markdown={questionnaire.introduction} />
        </div>
      )} */}

      {questionnaire && questionnaire.sections.map((section, index) => (
        <div key={index} className="bg-white shadow-xl rounded-2xl p-4 mb-4">
          <QuestionnaireSection
            ref={(e) => {
              if (e) {
                formRef.current[index] = e;
              }
            }}
            section={section}
          />
        </div>
      ))}
    </div>

  );
};

export default QuantityQuestionnaire;
