'use client';

import { useCoAgent } from '@copilotkit/react-core';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import ScreeningOutcomeMetrics from './ScreeningOutcomeMetrics';
import React, { useEffect, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { useCurrentStep, useCurrentTask, useEvaluationActions, useOverallScore, useScoreDetail, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import type { ScoreDetail } from '@/features/project-management/types/evaluation';
import ScreenOutcomeCharts from './ScreenOutcomeCharts';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import type { stateRouteAgent } from '@/shared/types/global';
import type { assessmentStateFlow } from '@/features/project-management/types/agent';
import { GET_POSITION_BY_TYPE } from '@/features/project-management/constants/evaluation';
import type { OverallScore } from '@/features/project-management/types/evaluation-form';
import { copilotkitAction } from '@/actions/copilotkit/copilotkit-actions';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { toast } from 'sonner';
import { useParams } from 'next/navigation';

export default function ScreeningOutcomeWrapper() {
  const [_isLoadingData, setIsLoadingData] = useState<boolean>(true);

  const [isCallData, setIsCallData] = useState<boolean>(false);

  const [dataAnalysis, setDataAnalysis] = useState<any | null>(null);

  const { mutateAsync } = useUpdateStatusStep();

  const { state: _stateAssessmentState } = useCoAgent<stateRouteAgent<assessmentStateFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {},
  });

  const overallScore = useOverallScore();

  const params = useParams<{ id: string }>();

  const {
    updateInitialEvaluationData,
    updateScoreDetail,
  } = useEvaluationActions();

  const scoreDetailStore = useScoreDetail();

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const { data: outComeData } = useGetInfoDetail<any, ScoreDetail>(currentStep?.id ?? '');

  const hideLoadingComponent = () => {
    const setStatusLoading = () => {
      setIsLoadingData(false);
    };
    setStatusLoading();
  };

  const setValueFiveTStateAgent = (overallScore: OverallScore) => {
    const { data } = overallScore;
    const items = Object.values(data).flat();
    return items.reduce((acc, i) => {
      const positionKey = GET_POSITION_BY_TYPE[i.type as keyof typeof GET_POSITION_BY_TYPE];
      acc[positionKey] = { ...i, id: positionKey.toString() };
      return acc;
    }, {} as Record<string, typeof items[number]>);
  };

  useEffect(() => {
    if (overallScore.score && outComeData && !outComeData.stepInfo.length && !isCallData) {
      const five_t_input = setValueFiveTStateAgent(overallScore);
      const data = {
        project_id: params.id,
        five_t_input,
      };

      const getInfoAnalysis = async () => {
        try {
          const response = await copilotkitAction(data, EEndpointApiCopilotkit.ASSESSMENT);

          const analysis = response.data.data;
          setDataAnalysis(analysis);

          hideLoadingComponent();
        } catch (error: any) {
          toast.error(error.message, {
            duration: 3000,
          });
        }
      };
      getInfoAnalysis();
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsCallData(true);
    } else if ((outComeData && outComeData.stepInfo.length)) {
      hideLoadingComponent();
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsCallData(true);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [overallScore.score, outComeData]);

  useEffect(() => {
    if (scoreDetailStore) {
      updateInitialEvaluationData(scoreDetailStore as any);
    }
  }, [scoreDetailStore]);

  // TODO: enhance it later
  useEffect(() => {
    if (outComeData && outComeData.stepInfoPrevious) {
      const data = { stepInfo: outComeData.stepInfoPrevious };
      updateScoreDetail(data as any);
    }
  }, [outComeData, updateScoreDetail]);

  // FIXME: update later when using Copilotkit
  // useEffect(() => {
  //   const assessmentState = stateAssessmentState[ENameStateAgentCopilotkit.ASSESSMENT];
  //   if ((assessmentState && assessmentState.client_assessment_process && assessmentState.client_assessment_process === 'done')
  //     || (outComeData && outComeData.stepInfo.length)
  //   ) {
  //     hideLoadingComponent();
  //   }
  // }, [stateAssessmentState, outComeData]);

  const handleSubmit = () => {
    if (currentStep?.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentStep?.id ?? '', EStatusTask.COMPLETED);
    }

    if (currentTask && currentTask.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }
    completeStep(currentStep?.id ?? '');
  };

  return (
    _isLoadingData
      ? (
          <div className=" h-full p-4 md:p-6 ">
            <ProjectCardSkeleton />
          </div>
        )
      : (
          <div className="relative">
            <div className="space-y-7 p-4 md:p-6">
              <ScreeningOutcomeMetrics data={dataAnalysis} />
              <ScreenOutcomeCharts />
            </div>

            <WorkflowNavigation
              onComplete={handleSubmit}
              nextButtonText="Complete & Move to Next Task"
              showPrevious={false}
            />
          </div>
        )
  );
}
