/* eslint-disable react-hooks-extra/no-direct-set-state-in-use-effect */
'use client';

import React, { useEffect, useState } from 'react';
import { ChartBarIcon, ChartPieIcon, GroupIcon, StarIcon } from '@/shared/icons';
import { useCurrentStep, useEvaluationActions, useOverallScore } from '../../../stores/project-workflow-store';
import { Tooltip } from '@/shared/components/ui/tooltip';
import { useCoAgent } from '@copilotkit/react-core';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { ScoreDetail } from '@/features/project-management/types/evaluation';
import type { stateRouteAgent } from '@/shared/types/global';
import type { assessmentStateFlow } from '@/features/project-management/types/agent';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';

// Function to determine conclusion based on score and rank
const getConclusion = (score: number, rank: string): string => {
  if (score >= 4.0 && ['A', 'A+'].includes(rank)) {
    return 'Strategic clients – prioritized for long-term investment';
  } else if (score >= 3.0 && ['B', 'B+'].includes(rank)) {
    return 'Potential clients – possible to further develop';
  } else if (score >= 2.0 && ['C', 'C+'].includes(rank)) {
    return 'Non-priority clients – need further consideration';
  } else {
    return 'Non-priority clients – need further consideration';
  }
};

function ShowDimensionPriorityScore({ listScore }: { listScore: any[] }) {
  return (
    <div className="w-full">
      {listScore?.map((dimension, index) => (
        <div className="w-full" key={index}>
          <h6>{dimension.dimension}</h6>
          <div className="grid grid-cols-9">
            <div className="col-span-1">
              <div className="w-full">Score:</div>
              <p className="">{(+dimension.score).toFixed(2)}</p>
            </div>
            <div className="col-span-1">
              <div className="w-full">Weight:</div>
              <p>{(+dimension.weight).toFixed(2)}</p>
            </div>
            <div className="col-span-1">
              <div className="w-full">Weighted Score:</div>
              <p>{(+dimension.weightedScore).toFixed(2)}</p>
            </div>
            <div className="col-span-1"></div>
            <div className="col-span-5">
              <div className="w-full">Comment:</div>
              <p className="truncate">
                {dimension.comment}
                {' '}
              </p>
            </div>
          </div>

        </div>
      ))}
    </div>
  );
}

export default function ScreeningOutcomeMetrics({ data }: { data: any }) {
  // Get data from the evaluation store
  const overallScore = useOverallScore();
  const { getFinalOverallScore } = useEvaluationActions();

  const [listRisk, setListRisk] = useState<any[]>([]);
  const [listOfSuggestion, setListOfSuggestion] = useState<any[]>([]);
  const [priorityScore, setPriorityScore] = useState<string>('0');
  const [dimensionScore, setDimensionScore] = useState<any[]>([]);

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const currentStep = useCurrentStep();

  const { data: outComeData } = useGetInfoDetail<any, ScoreDetail>(currentStep?.id ?? '');

  const { state: _stateAssessmentState } = useCoAgent<stateRouteAgent<assessmentStateFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {},
  });

  const saveDataFiveT = (data: any) => {
    const payload = {
      stepInfos: Object.values(data).map((item: any, index: number) => ({
        order: index,
        infos: [{ ...item }],
      })),
    };

    updateQuestionAnswer(payload, currentStep?.id ?? '');
  };

  const setDataInTable = (suggestion: any[], risks: any[], priorityScore: string, dimensionScore: any[]) => {
    setListOfSuggestion(suggestion);
    setListRisk(risks);
    setDimensionScore(dimensionScore);
    setPriorityScore(priorityScore);
  };

  useEffect(() => {
    if (data) {
      const { engagement_strategy, priorityScore, risk_assessment, totalScore } = data;
      setDataInTable(engagement_strategy, risk_assessment, totalScore, priorityScore);
      saveDataFiveT({ dimensions: { priorityScore: totalScore, dimensionScore: priorityScore }, listOfSuggestion: engagement_strategy, listRisk: risk_assessment });
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data]);

  useEffect(() => {
    if (outComeData && (outComeData.stepInfo.length)) {
      const infosOutComeData = outComeData.stepInfo.slice();
      const priorityScore = infosOutComeData[0].infos[0];
      const suggestion = Object.values(infosOutComeData[1].infos[0]).map(data => data);
      const risks = Object.values(infosOutComeData[2].infos[0]).map(data => data);
      setDataInTable(suggestion, risks, priorityScore.priorityScore, priorityScore.dimensionScore);
    }
  }, [outComeData]);

  // useEffect(() => {
  //   const assessmentState = stateAssessmentState[ENameStateAgentCopilotkit.ASSESSMENT];
  //   if (assessmentState && assessmentState.client_assessment_process && assessmentState.client_assessment_process === 'done' && !isGetData) {
  //     const listRisk = (assessmentState.risk_output).risk_assessment;
  //     const listOfSuggestion = (assessmentState.engagement_output).engagement_strategy;
  //     const priorityScore = (assessmentState.priority_score_output).totalScore;
  //     const dimensionScore = (assessmentState.priority_score_output).priorityScore;
  //     saveDataFiveT({ dimensions: { priorityScore, dimensionScore }, listOfSuggestion, listRisk });
  //     setDataInTable(listOfSuggestion, listRisk, priorityScore, dimensionScore);
  //   }
  // }, [stateAssessmentState, isGetData, setIsGetData, setListRisk, setListOfSuggestion, setPriorityScore, setDimensionScore]);

  // Calculate the final score when the component mounts and whenever it's shown
  useEffect(() => {
    getFinalOverallScore();
  }, [getFinalOverallScore]);

  // Force a recalculation every second to ensure we have the latest data
  useEffect(() => {
    const intervalId = setInterval(() => {
      getFinalOverallScore();
    }, 1000);

    return () => clearInterval(intervalId);
  }, [getFinalOverallScore]);

  // Extract values from the store
  const score = overallScore.score || 0;
  const rank = overallScore.rank || 'N/A';
  const conclusion = getConclusion(score, rank);

  return (
    <>
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-6 xl:grid-cols-7">
        <div className="rounded-2xl border border-border bg-background p-5">
          <p className="text-muted-foreground text-theme-sm">Score</p>
          <div className="flex items-end justify-between mt-3">
            <div>
              <h6>
                {score.toFixed(1)}
              </h6>
            </div>

            <div className="flex items-center justify-center size-12 text-success-500 bg-success-500/[0.08] rounded-xl">
              <ChartPieIcon className="size-6" />
            </div>
          </div>
        </div>

        <div className="rounded-2xl border border-border bg-background p-5">
          <p className="text-muted-foreground text-theme-sm">Rank</p>
          <div className="flex items-end justify-between mt-3">
            <div>
              <h6>
                {rank}
              </h6>
            </div>

            <div className="flex items-center justify-center size-12 text-blue-500 bg-blue-500/[0.08] rounded-xl">
              <ChartBarIcon className="size-6" />
            </div>
          </div>
        </div>

        <Tooltip className="w-[740px]" position="bottom" content={<ShowDimensionPriorityScore listScore={dimensionScore} />}>
          <div className="rounded-2xl border border-border bg-background p-5 cursor-pointer">
            <p className="text-muted-foreground text-theme-sm">Priority score</p>
            <div className="flex items-end justify-between mt-3">
              <div>
                <h6>
                  {(+priorityScore).toFixed(2)}
                </h6>
              </div>

              <div className="flex items-center justify-center size-12 text-blue-500 bg-blue-500/[0.08] rounded-xl">
                <StarIcon className="size-6" />
              </div>
            </div>
          </div>
        </Tooltip>

        <div className="rounded-2xl border border-border bg-background p-5 xl:col-span-4">
          <p className="text-muted-foreground text-theme-sm">Conclusion</p>
          <div className="flex items-end justify-between mt-3">
            <div>
              <h6>
                {conclusion}
              </h6>
            </div>

            <div>
              <div className="flex items-center justify-center size-12 text-error-500 bg-error-500/[0.08] rounded-xl">
                <GroupIcon className="size-6" />
              </div>
            </div>
          </div>
        </div>

      </div>

      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-6 xl:grid-cols-4">
        <div className="rounded-2xl border border-error-50 bg-error-50 p-5 xl:col-span-2">
          <p className="text-error-500 text-theme-sm">Potential Risks</p>
          <div className="flex items-end justify-between mt-3">
            <div>
              {(
                listRisk.map((risk, index) => (
                  <div key={index}>
                    <div className="font-semibold">
                      {risk.risk_name}
                      :
                    </div>
                    <ul className="list-disc px-8">
                      <li>
                        {risk.justification}
                      </li>
                    </ul>
                  </div>

                ))
              )}

            </div>

          </div>
        </div>

        <div className="rounded-2xl border border-info-50 bg-info-50 p-5 xl:col-span-2">
          <p className="text-info-500 text-theme-sm">Suggested tailored approaches</p>
          <div className="flex items-end justify-between mt-3">
            <div>
              {(
                listOfSuggestion.map((suggestion, index) => (
                  <div key={index}>
                    <div className="font-semibold">
                      {suggestion.dimension}
                      :
                    </div>
                    <ul className="px-8 list-disc">
                      <li>
                        Action :
                        {' '}
                        {suggestion.action}
                        {suggestion.kpi}
                      </li>
                      <li>
                        KPI :
                        {' '}
                        {suggestion.kpi}
                      </li>
                    </ul>
                  </div>
                ))
              )}
            </div>

          </div>
        </div>
      </div>
    </>
  );
}
