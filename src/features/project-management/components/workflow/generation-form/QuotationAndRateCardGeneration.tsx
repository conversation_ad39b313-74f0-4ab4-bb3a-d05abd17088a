'use client';

import { <PERSON><PERSON> } from '@/shared/components/ui/button';
import { CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import { useEffect, useState } from 'react';
import { useCoAgent } from '@copilotkit/react-core';
import type { quotationOfWorkFlow } from '@/features/project-management/types/agent';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import Editor from '@/shared/components/ui/editor/editor';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { useCurrentStep, useCurrentTask, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { EEndpointApiCopilotkit, ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import { ETypeFile } from '@/features/project-management/types/project';
import type { ProjectCampaignEnum, stepInfosMarkdownResponse, TemplateFiles } from '@/features/project-management/types/project';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import { useParams } from 'next/navigation';
import { copilotkitAction } from '@/actions/copilotkit/copilotkit-actions';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';

const QuotationRatingGeneration: React.FC = () => {
  const [isShowEditButton, setIsShowEditButton] = useState(false);

  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [campaignSelected, setCampaignSelected] = useState<ProjectCampaignEnum | null>(null);

  const [_templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;

  const workflow = useWorkflowTasks();

  // FIXME: update later
  const idSecondStep = workflow[1]?.steps[0]?.id;

  const currentTask = useCurrentTask();

  const { mutateAsync } = useUpdateStatusStep();

  const {
    completeStep,
    updateStatus,
  } = useWorkflowActions();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const params = useParams<{ id: string }>();

  const { state } = useCoAgent<stateRouteAgent<quotationOfWorkFlow>>({
    name: AGENT_ROUTE_NAME,
  });
  const { data: clientUploadData } = useGetInfoDetail<any, any>(idSecondStep ?? '');

  const { data: templates } = useGetListTemplates();

  const { data: quotationResponse } = useGetInfoDetail<stepInfosMarkdownResponse, stepInfosMarkdownResponse>(currentStepId ?? '');

  useEffect(() => {
    if (clientUploadData?.stepInfo.length && clientUploadData?.stepInfo[0]?.infos?.length) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCampaignSelected(clientUploadData?.stepInfo[0]?.infos[0]?.serviceOption);
    }
  }, [clientUploadData]);

  useEffect(() => {
    if (templates && campaignSelected) {
      const templateSelect = templates.filter(template => template.campaign === campaignSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);
      setTemplateFile(urlOptions);
    }
  }, [templates, campaignSelected]);

  const saveDataFromAI = async (markdown: string) => {
    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };

    if (currentStepId) {
      await updateQuestionAnswer(payload, currentStepId);
      mutateAsync({ id: currentStepId, status: EStatusTask.IN_PROGRESS });
    }
  };

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  const getGenerateData = async (data: any) => {
    try {
      const response = await copilotkitAction(data, EEndpointApiCopilotkit.QUOTATION);
      const brief = response.data.data;
      updateMarkdownToState(brief);
      saveDataFromAI(brief);
    } catch (error: any) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (quotationResponse && quotationResponse?.stepInfo.length) {
      const markdown = quotationResponse.stepInfo[0]?.infos[0]?.value;

      updateMarkdownToState(markdown ?? '');
      if (currentStep?.status !== EStatusTask.COMPLETED) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowEditButton(false);
      } else {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowEditButton(true);
      }
    } else {
      if (quotationResponse && quotationResponse?.stepInfoPrevious.length) {
        const briefFile = quotationResponse?.stepInfoPrevious?.[0]?.infos?.[0]?.value ?? '';

        if (!_templateFile.length) {
          return;
        }
        const data = {
          project_id: params.id,
          sow_analysis: briefFile,
          ..._templateFile.reduce((result, template) => {
            if (template.type === ETypeFile.QUOTATION) {
              result.quotation_template_url = [
                ...(result.quotation_template_url || []),
                ...getFile([template.file]),
              ];
            }

            return result;
          }, {} as any),
        };
        getGenerateData(data);
      }
    }
  }, [quotationResponse, _templateFile]);

  useEffect(() => {
    const quotationState = state[ENameStateAgentCopilotkit.QUOTATION];
    if (quotationState?.quotation_analysis_output && quotationState.quotation_analysis_process && quotationState.quotation_analysis_process === 'done') {
      updateMarkdownToState(quotationState?.quotation_analysis_output);
    }
  }, [state]);

  const toggleEditMode = () => {
    setIsEditMode(true);
  };

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    setIsEditMode(false);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);
    if (currentStep.status !== EStatusTask.COMPLETED) {
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });

      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });

      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }

    completeStep(currentStepId);
  };

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);
  };

  const discardChange = () => {
    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    setMarkdown(form);
    setIsEditMode(false);
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">Analyzing</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6 z-1">
            {isEditMode
              ? (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={discardChange}
                    >
                      Discard Change
                    </Button>

                    <Button
                      type="button"
                      onClick={confirmChange}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Confirm
                    </Button>
                  </>
                )
              : (
                  <>
                    {!isShowEditButton && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={toggleEditMode}
                      >
                        <FileEditIcon className="h-5 w-5 " />
                        Edit
                      </Button>
                    )}

                    {/* <Button
                      type="button"
                      variant="outline"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button> */}
                    <Button
                      type="button"
                      onClick={handleSubmit}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  </>
                )}

          </div>

          <div className="mt-6">
            {
              isEditMode
                ? <Editor onChange={handleChangeEditor} value={form} />

                : <MarkdownRenderer content={markdown} />
            }
          </div>
        </div>
      );
};

export default QuotationRatingGeneration;
