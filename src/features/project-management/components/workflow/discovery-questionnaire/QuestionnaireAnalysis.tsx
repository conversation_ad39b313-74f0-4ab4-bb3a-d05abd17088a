import { Button } from '@/shared/components/ui/button';
import Editor from '@/shared/components/ui/editor/editor';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { ArrowUturnLeftIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import type { EditorContentChanged } from '@/shared/types/global';
import { useEffect, useState } from 'react';

type AnalysisDataViewType = {
  data: string;
  isHiddenApproveButton?: boolean;
  onSubmit: (markdown: string) => void;
  onBack?: () => void;
  onConfirm?: (markdown: string) => void;
};
const QuestionnaireAnalysis: React.FC<AnalysisDataViewType> = ({
  data,
  isHiddenApproveButton,
  onSubmit,
  onBack,
  onConfirm,
}) => {
  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [isShowEditButton, _setIsShowEditButton] = useState(false);

  const [form, setForm] = useState<string>('');

  const [markdown, setMarkdown] = useState<string>('');

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setMarkdown(data);
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setForm(data);
  }, [data]);

  const discardChange = () => {
    setForm(data);
    setIsEditMode(false);
  };

  const toggleEditMode = () => {
    setIsEditMode(true);
  };

  const confirmChange = () => {
    setMarkdown(form);
    setIsEditMode(false);
    if (onConfirm) {
      onConfirm(form);
    }
  };

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);
  };

  return (
    <div className="relative">
      <div>
        { isEditMode
          ? <Editor onChange={handleChangeEditor} value={markdown} />

          : <MarkdownRenderer content={markdown} />}
      </div>

      {markdown && (
        <div className="bg-white w-full p-2 flex items-center gap-4 justify-center sticky bottom-0">
          {isEditMode
            ? (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={discardChange}
                  >
                    Discard Change
                  </Button>

                  <Button
                    type="button"
                    onClick={confirmChange}
                  >
                    <CheckBadgeIcon className="h-5 w-5 " />
                    Confirm
                  </Button>
                </>
              )
            : (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                  >
                    <ArrowUturnLeftIcon className="h-5 w-5 " />
                    Back
                  </Button>

                  {!isShowEditButton && (
                    <Button
                      type="button"
                      variant="outline"
                      onClick={toggleEditMode}
                    >
                      <FileEditIcon className="h-5 w-5 " />
                      Edit
                    </Button>
                  )}

                  {/* <Button
                      type="button"
                      variant="outline"
                    >
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button> */}
                  {!isHiddenApproveButton && (
                    <Button
                      type="button"
                      onClick={() => onSubmit(markdown)}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      Approve
                    </Button>
                  )}
                </>
              )}

        </div>
      )}
    </div>
  );
};

export default QuestionnaireAnalysis;
