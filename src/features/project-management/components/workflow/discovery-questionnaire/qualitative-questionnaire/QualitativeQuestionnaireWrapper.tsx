'use client';

import Label from '@/shared/components/form/Label';
import FileUpload from '../../initial-screening-form/FileUpload';
import React, { useEffect, useState } from 'react';
import type { IFileResponse } from '@/shared/types/global';
import { Button } from '@/shared/components/ui/button';
import { useParams } from 'next/navigation';
import { getFile } from '@/features/project-management/utils/initialScreeningUtils';
import { copilotkitAction } from '@/actions/copilotkit/copilotkit-actions';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import type { TypeStepInfosRes } from '@/features/project-management/types/evaluation';
import { useCurrentStep } from '@/features/project-management/stores/project-workflow-store';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import QualitativeAnalysis from './QualitativeAnalysis';

type QuantitativeQuestionnaireWrapperType = {
  fileResponse: IFileResponse[];
  markdown: string;
  loading: boolean;
  isAnalysis: boolean;
  stepInfos: TypeStepInfosRes[];
  onLoading: (isLoading: boolean) => void;
  setIsAnalysis: (isAnalysis: boolean) => void;
  saveStepInfos: (data: TypeStepInfosRes[]) => void;
};

const QualitativeQuestionnaireWrapper: React.FC<QuantitativeQuestionnaireWrapperType> = ({
  markdown: initialMarkdown,
  loading,
  fileResponse,
  stepInfos,
  isAnalysis,
  onLoading,
  saveStepInfos,
  setIsAnalysis,
}) => {
  const [files, setFiles] = useState<IFileResponse[]>([]);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  const [markdown, setMarkdown] = useState<string>('');

  const [isShowSubmitButton, setIsShowSubmitButton] = useState<boolean>(true);

  const params = useParams<{ id: string }>();

  const currentStep = useCurrentStep();

  const currentStepId = currentStep?.id ?? '';

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const isHidden = stepInfos?.find(info => info?.order === 1)?.infos[0]?.isSaved ?? false;

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setIsShowSubmitButton(isHidden);
  }, [isHidden]);

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setMarkdown(initialMarkdown);
  }, [initialMarkdown]);

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setInitialFile(fileResponse);
  }, [fileResponse]);

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const saveDataFromAI = async (markdown: string, isSaved: boolean) => {
    const stepInfosData = [
      ...stepInfos.filter(infos => infos.order === 0),
      {
        order: 1,
        infos: [{ value: markdown, files, isSaved }],
      },
    ];

    const payload = {
      stepInfos: stepInfosData,
    };

    await updateQuestionAnswer(payload, currentStepId);

    saveStepInfos(stepInfosData);
  };

  const handleGenerateData = async () => {
    onLoading(true);
    setIsAnalysis(true);
    const payload = {
      project_id: params.id ?? '',
      conversation_transcript_url: [...getFile(files)],
      quality_questionnaire_form: '',
    };

    const response = await copilotkitAction(payload, EEndpointApiCopilotkit.SUMMARY_QUALITY);
    if (!response) {
      return;
    }
    const markdown = response.data.data;
    saveDataFromAI(markdown, false);
    setMarkdown(markdown);
    onLoading(false);
  };

  const handleSubmitData = async (data: string) => {
    setMarkdown(data);
    await saveDataFromAI(data, true);
  };

  const handleConfirm = (data: string) => {
    const normalize = (str: string) => str.trim().replace(/\r\n/g, '\n').replace(/\s+/g, ' ');
    const isChanged = normalize(data) !== normalize(markdown);
    console.log(isChanged);
  };

  const handleBackUpload = () => {
    setIsAnalysis(false);
  };

  return (
    loading
      ? (
          <div className="mt-4 w-full">
            Loading...
            <ProjectCardSkeleton />
          </div>
        )
      : (
          <div className="w-full relative mt-4">

            {!isAnalysis
              ? (
                  <>
                    <Label htmlFor="files" className="mb-1.5 block text-primary">
                      Attached Files
                    </Label>
                    <FileUpload onFilesChange={handleFilesChange} initialFile={initialFile} />

                    <div className="sticky bottom-0 flex items-center justify-center p-4">
                      <Button onClick={handleGenerateData}>
                        Generate
                      </Button>
                    </div>
                  </>
                )
              : (
                  <QualitativeAnalysis
                    markdown={markdown}
                    isHidden={isShowSubmitButton}
                    onSubmit={handleSubmitData}
                    onBack={handleBackUpload}
                    onConfirm={handleConfirm}
                  />
                )}

          </div>
        )
  );
};

export default QualitativeQuestionnaireWrapper;
