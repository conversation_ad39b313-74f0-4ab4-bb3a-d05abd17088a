'use client';

import ProjectCardSkeleton from '@/features/project-management/components/project-list/ProjectCardSkeleton';
import { useProjectSurvey } from '@/features/project-management/hooks';
import PieChartSurveyInfo from './PieChartSurveyInfo';
import { QuestionType } from '@/features/project-management/types/workflow';
import HorizontalBarChartInfo from './HorizontalBarChart';
import VerticalBarChartInfo from './VerticalBarChart';
import ListHorizontalBarChartInfo from './ListHorizontalBarChart';

type ChartSurveyWrapperType = {

  idQuestionnaire: string;

};

const ChartSurveyWrapper: React.FC<ChartSurveyWrapperType> = ({
  idQuestionnaire,
}) => {
  const { data: statisticVisual } = useProjectSurvey(idQuestionnaire);

  console.log(statisticVisual);

  return (
    <div className="w-full relative">

      {
        !statisticVisual
          ? (
              <div className="mt-4">
                <div className="mb-2">Loading...</div>
                <ProjectCardSkeleton />
              </div>
            )
          : (
              statisticVisual.sections.map((section, index) => (
                <div key={index} className="p-4 rounded-xl bg-gray-50 w-full mt-4 ">
                  <div className=" p-1.5 px-3 border-l-4 border-black">
                    <h4 className="text-black">{section.title}</h4>
                    {/* <div
                      className=""
                    >
                      Please provide us your basic information for survey screening process.
                    </div> */}
                  </div>

                  {section.questions.map((question, idx) => (
                    <div key={idx} className="my-4">
                      {question.type === QuestionType.RADIO && (
                        <PieChartSurveyInfo title={question.title} data={question.statitics} />
                      )}

                      {question.type === QuestionType.CHECKBOX && (
                        <HorizontalBarChartInfo title={question.title} data={question.statitics} />
                      )}

                      {question.type === QuestionType.EVALUATION && (
                        <VerticalBarChartInfo title={question.title} legendLabel={question.options} data={question.statitics} />
                      )}

                      {question.type === QuestionType.CATEGORY && (
                        <ListHorizontalBarChartInfo title={question.title} subQuestions={question.subQuestions} />
                      )}
                    </div>
                  ))}
                </div>
              ))

            )
      }
    </div>

  );
};

export default ChartSurveyWrapper;
