'use client';
import { Button } from '@/shared/components/ui/button';
import { CircleCheckIcon } from '@/shared/icons';
import { useParams } from 'next/navigation';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { copilotkitAction } from '@/actions/copilotkit/copilotkit-actions';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { useEffect, useState } from 'react';
import { useCurrentStep } from '@/features/project-management/stores/project-workflow-store';
import type { TypeStepInfosRes } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import QuestionnaireAnalysis from '../QuestionnaireAnalysis';

type QuantitativeAnalysisType = {
  markdown: string;
  isLoading: boolean;
  isAnalysis: boolean;
  stepInfos: TypeStepInfosRes[];
  setIsLoading: (status: boolean) => void;
  setIsAnalysis: (status: boolean) => void;
  saveStepInfos: (stepInfos: TypeStepInfosRes[]) => void;
};

const QuantitativeAnalysis: React.FC<QuantitativeAnalysisType> = ({
  markdown: dataMarkdown,
  isLoading,
  isAnalysis,
  stepInfos,
  setIsLoading,
  setIsAnalysis,
  saveStepInfos,
}) => {
  const params = useParams<{ id: string }>();

  const [markdown, setMarkdown] = useState<string>('');

  const currentStep = useCurrentStep();

  const currentStepId = currentStep?.id;

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const isHidden = stepInfos?.find(info => info?.order === 0)?.infos[0]?.isSaved ?? false;

  useEffect(() => {
    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    setMarkdown(dataMarkdown);
  }, [dataMarkdown]);

  const saveDataFromAI = async (markdown: string, isSaved: boolean) => {
    const stepInfosData = [
      ...stepInfos.filter(infos => infos.order === 1),
      {
        order: 0,
        infos: [{ value: markdown, isSaved }],
      },
    ];

    const payload = {
      stepInfos: stepInfosData,
    };

    await updateQuestionAnswer(payload, currentStepId ?? '');

    saveStepInfos(stepInfosData);
  };

  const handleGetAnalysis = async () => {
    setIsLoading(true);
    setIsAnalysis(true);
    const payload = {
      project_id: params.id,
      quantity_questionnaire_survey: [],
    };

    const response = await copilotkitAction(payload, EEndpointApiCopilotkit.SUMMARY_QUANTITY);
    if (!response) {
      return;
    }
    const markdown = response.data.data;
    setMarkdown(markdown);
    saveDataFromAI(markdown, false);
    setIsLoading(false);
  };

  const handleSubmitData = async (data: string) => {
    setMarkdown(data);

    saveDataFromAI(markdown, true);
  };

  return (
    isLoading
      ? (
          <div className="mt-4 w-full">
            Loading...
            <ProjectCardSkeleton />
          </div>
        )
      : (
          isAnalysis

            ? (
                <div className="mt-4 relative">
                  <QuestionnaireAnalysis
                    data={markdown}
                    isHiddenApproveButton={isHidden}
                    onBack={() => setIsAnalysis(false)}
                    onSubmit={handleSubmitData}
                  />
                </div>
              )

            : (
                <div className="w-full mt-4 bg-gray-50 rounded-xl border border-bg-gray-100 flex gap-3 flex-col items-center justify-between py-12">
                  <div>
                    Generate analysis for this questionnaire?
                  </div>

                  <div className="text-xs text-gray-500">
                    The questionnaire status will be changed to
                    {' '}
                    <span className="font-medium text-black">Closed</span>
                    {' '}
                    once click
                    {' '}
                    <span className="font-medium text-black">Confirm</span>
                    .
                  </div>

                  <Button onClick={handleGetAnalysis}>
                    <CircleCheckIcon className="h-5 w-5" />
                    Confirm
                  </Button>

                </div>
              )
        )

  );
};

export default QuantitativeAnalysis;
