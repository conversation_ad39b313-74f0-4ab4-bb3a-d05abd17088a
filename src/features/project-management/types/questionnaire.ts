export type OptionSelectType = {
  id: number;
  label: string;
  description: string;
  value: EOptionSelect.FORM | EOptionSelect.MARKDOWN;
  isSelected: boolean;
};

export enum EOptionSelect {
  FORM = 'form',
  MARKDOWN = 'markdown',
}

export type QuestionnaireType = 'quantitative' | 'qualitative';

export type toggleOptionsType = {
  id: QuestionnaireType;
  label: string;
  type: EOptionSelect;
};
