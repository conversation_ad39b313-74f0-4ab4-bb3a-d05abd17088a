import type { StepInfosQA } from './step';
// import type { Step as WorkflowStep, Task as WorkflowTask } from '../types/workflow';

// Step types
export enum ETypeStep {
  FORM = 'form',
  EVALUATION = 'evaluation',
  OUTCOME = 'outcome',
  ANALYSIS = 'analysis',
  GENERATION = 'generation',
  UPLOAD = 'upload',
  GENERATE = 'generate',
  FINALIZE = 'finalize',
  REVIEW_1 = 'review 1',
  GENERATED = 'generated',
  REVIEW_2 = 'review 2',
  GENERATED2 = 'generated 2',
  STANDARDIZED = 'standardized',
  DOCUMENT = 'document',
  QUESTIONNAIRE = 'questionnaire',
  REPORT = 'report',
}

// Task and step status
export enum EStatusTask {
  PENDING = 'pending',
  IN_PROGRESS = 'in-progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
}

// Step definition
// export type Step = {
//   id: string;
//   name: string;
//   description: string;
//   status: EStatusTask;
//   type: ETypeStep;
//   position: number;
//   data?: any; // Data associated with the step
//   qaList?: StepInfosQA[]; // Data associated with the step
//   positionIdx: number;
// };

// Task definition
// export type Task = {
//   id: string;
//   name: string;
//   description: string;
//   status: EStatusTask;
//   position: number;
//   steps: Step[];
// };
export type WorkflowStep = {
  // subs?: stepData[];
  id: string;
  name: string;
  projectId: string;
  order: number;
  status: EStatusTask;
  type?: ETypeStep;
  data?: any; // Data associated with the step
  qaList?: StepInfosQA[]; // Data associated with the step

  prompts: any[];
  promptIds: string[]; // Same as above

  stepInfos: StepInfos[];

  parent?: string; // Optional, since not all steps have a parent

  createdAt: string;
  updatedAt: string;

  steps: WorkflowStep[];
  children: WorkflowStep[];
};

export type StepInfos = {
  clientId: string;
  question: string;
  answer: string;
};

// Workflow definition
export type Workflow = {
  id: string;
  name: string;
  description: string;
  tasks: WorkflowStep[];
};

export type NavigationTarget = {
  taskId: string;
  stepId?: string;
};

export type WorkflowMaps = {
  taskMap: Map<string, { task: WorkflowStep; index: number }>;
  stepMap: Map<string, { task: WorkflowStep; step: WorkflowStep; taskIndex: number; stepIndex: number }>;
  taskOrder: string[];
};

export type Files = {
  id: string;
  url: string;
  originalname: string;
  filename: string;
  key: string;
};

export type StatisticVisualType = {
  sections: SectionStatisticType[];
};

export type SectionStatisticType = {
  title: string;
  questions: QuestionSectionsType[];
};

export type QuestionSectionsType = {
  form: string;
  id: string;
  other: string[];
  questionId: string;
  statitics: any[];
  subQuestions: SubQuestionsStatisticType[];
  title: string;
  total: number;
  type: QuestionType;
  options: string[];
};

export enum QuestionType {
  TEXT = 'text',
  MULTIPLE_TEXT = 'multiple_text',
  RADIO = 'radio',
  CHECKBOX = 'checkbox',
  CATEGORY = 'category',
  EVALUATION = 'evaluation',
}

export type SubQuestionsStatisticType = {
  type: string;
  title: string;
  statitics: any[];
};
