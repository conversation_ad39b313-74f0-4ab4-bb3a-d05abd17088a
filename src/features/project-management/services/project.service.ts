import type { ApiResponse } from '@/shared/types/api-response';
import type { CreateScreeningOutcome, ScoreDetail, StepInfosPayload, TemplatesResponse } from '../types/evaluation';
import type { GetProjectsResponse, Project, ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '../types/project';
import type {
  StepQA,
  // WorkflowStep
} from '../types/step';
import type { CreateProjectPayload } from '../validation/project.validation';
import { http } from '@/core/http/http';
import { DEFAULT_PAGE_SIZE } from '../constants';
import type { EStatusTask, StatisticVisualType, WorkflowStep } from '../types/workflow';

export type ProjectFilters = {
  status?: ProjectStatusEnum | 'All';
  type?: ProjectTypeEnum | 'All';
  campaign?: ProjectCampaignEnum | 'All';
  startDate?: Date;
  endDate?: Date;
  searchQuery?: string;
  page?: number;
  limit?: number;
};

/**
 * Builds query parameters for project API requests based on filter criteria
 */
export const buildProjectQueryParams = (filters: ProjectFilters = {}): string => {
  const params = new URLSearchParams();

  if (filters.status && filters.status !== 'All') {
    params.append('status', filters.status.toString());
  }

  if (filters.type && filters.type !== 'All') {
    params.append('type', filters.type.toString());
  }

  if (filters.campaign && filters.campaign !== 'All') {
    params.append('campaign', filters.campaign.toString());
  }

  if (filters.startDate) {
    params.append('startDate', filters.startDate.toISOString());
  }

  if (filters.endDate) {
    params.append('endDate', filters.endDate.toISOString());
  }

  if (filters.searchQuery) {
    params.append('searchValue', filters.searchQuery);
  }

  if (filters.page !== undefined) {
    params.append('page', filters.page.toString());
  }

  if (filters.limit !== undefined) {
    params.append('itemsPerPage', filters.limit.toString());
  }

  return params.toString() ? `?${params.toString()}` : '';
};

/**
 * Fetches projects from the API with filtering
 */
export async function getProjects(filters: ProjectFilters = {}): Promise<ApiResponse<GetProjectsResponse>> {
  const queryString = buildProjectQueryParams(filters);
  const response = await http.get<GetProjectsResponse>({ url: `/projects${queryString}` });
  return response;
}

/**
 * Fetches projects with pagination for infinite scrolling
 */
export async function getProjectsPage(
  filters: ProjectFilters = {},
  pageParam = 1,
): Promise<ApiResponse<GetProjectsResponse>> {
  try {
    const paginatedFilters = {
      ...filters,
      page: pageParam,
      limit: filters.limit || DEFAULT_PAGE_SIZE,
    };

    const queryString = buildProjectQueryParams(paginatedFilters);

    const response = await http.get<GetProjectsResponse>({ url: `/projects${queryString}` });

    return response;
  } catch (error) {
    console.error('[getProjectsPage] Error:', error);
    throw error;
  }
}

/**
 * Fetches a single project by ID
 */
export async function getProjectById(id: string): Promise<ApiResponse<Project>> {
  return await http.get<Project>({ url: `/projects/${id}` });
}

/**
 * Deletes a project by ID
 */
export async function deleteProjectById(id: string): Promise<ApiResponse<void>> {
  return await http.delete<void>({ url: `/projects/${id}` });
}

/**
 * Creates a new project
 */
export async function createProject(data: CreateProjectPayload): Promise<ApiResponse<Project>> {
  return await http.post<Project>({
    url: '/projects',
    data,
  });
}

export async function getProjectSteps(id: string): Promise<ApiResponse<WorkflowStep[]>> {
  return await http.get<WorkflowStep[]>({ url: `/steps/${id}/project` });
}

export async function getQAByStepId<T, D>(id: string): Promise<ApiResponse<StepQA<T, D>>> {
  return await http.get<StepQA<T, D>>({ url: `/steps/${id}` });
}

export async function createScreeningOutcome(data: CreateScreeningOutcome, id: string): Promise<ApiResponse<boolean>> {
  return await http.post<boolean>({
    url: `/scores/${id}`,
    data,
  });
}

export async function updateQuestionAnswerApi(data: StepInfosPayload, id: string): Promise<ApiResponse<boolean>> {
  return await http.post<boolean>({
    url: `/step-infos/${id}`,
    data,
  });
}

export async function getScoreDetail(projectId: string, id: string): Promise<ApiResponse<ScoreDetail[]>> {
  return await http.get<ScoreDetail[]>({ url: `/scores/${id}/step-info/${projectId}` });
}

export async function updateStatusStep(id: string, status: EStatusTask): Promise<ApiResponse<boolean>> {
  return await http.put<boolean>({
    url: `/steps/${id}/status`,
    data: {
      status,
    },
  });
}

export async function getListTemplate(): Promise<ApiResponse<TemplatesResponse[]>> {
  return await http.get<TemplatesResponse[]>({ url: `/templates` });
}

export async function getStatisticVisual(id: string): Promise<ApiResponse<StatisticVisualType>> {
  return await http.get<StatisticVisualType>({ url: `/questionnaires/${id}/statitics/visual` });
}

export async function getStatisticRaw(id: string): Promise<ApiResponse<StatisticVisualType>> {
  return await http.get<StatisticVisualType>({ url: `/questionnaires/${id}/statitics/raw` });
}
