import type { QuestionnaireResponse } from '@/features/questionnaire/types/questionnaire';
import type { SectionTotals } from '../stores/project-workflow-store';
import { calculateSectionScore, calculateSectionWeight, getRank } from '../stores/project-workflow-store/slices/createEvaluationSlice';
import type { QAStrategistOutput } from '../types/evaluation';
import { SectionType } from '../types/evaluation-form';
import type {
  NavigationTarget,
  WorkflowMaps,
  WorkflowStep,
  // Step as WorkflowStep,
  //  Task as WorkflowStep
} from '../types/workflow';
import { EStatusTask } from '../types/workflow';
import { mapEvaluationDataStore } from './evaluationMapper';
import { handleAddFieldNameInData } from '@/features/questionnaire/utils';

// ============================================================================
// PURE UTILITY FUNCTIONS (FUNCTIONAL APPROACH)
// ============================================================================

/**
 * Creates optimized lookup maps for O(1) access
 */
export const createWorkflowMaps = (workflow: WorkflowStep[]): WorkflowMaps => {
  const taskMap = new Map<string, { task: WorkflowStep; index: number }>();
  const stepMap = new Map<string, { task: WorkflowStep; step: WorkflowStep; taskIndex: number; stepIndex: number }>();
  const taskOrder: string[] = [];

  workflow.forEach((task, taskIndex) => {
    taskMap.set(task.id, { task, index: taskIndex });
    taskOrder.push(task.id);

    task.children.forEach((step, stepIndex) => {
      stepMap.set(step.id, { task, step, taskIndex, stepIndex });
    });
  });

  return { taskMap, stepMap, taskOrder };
};

/**
 * Gets step by ID using optimized lookup
 */
export const getStepById = (stepId: string, maps: WorkflowMaps): WorkflowStep | null => {
  return maps.stepMap.get(stepId)?.step || null;
};

/**
 * Finds the next task that's available for processing
 */
export const findNextAvailableTask = (currentTaskId: string, maps: WorkflowMaps): WorkflowStep | null => {
  const currentIndex = maps.taskOrder.indexOf(currentTaskId);
  if (currentIndex === -1) {
    return null;
  }

  for (let i = currentIndex + 1; i < maps.taskOrder.length; i++) {
    const taskId = maps.taskOrder[i];
    const taskInfo = maps.taskMap.get(taskId ?? '');

    if (taskInfo?.task.status) {
      return taskInfo.task;
    }
  }

  return null;
};

/**
 * Finds the previous task
 */
export const findPreviousTask = (currentTaskId: string, maps: WorkflowMaps): WorkflowStep | null => {
  const currentIndex = maps.taskOrder.indexOf(currentTaskId);
  if (currentIndex <= 0) {
    return null;
  }

  const prevTaskId = maps.taskOrder[currentIndex - 1];
  return maps.taskMap.get(prevTaskId ?? '')?.task || null;
};

/**
 * Calculates next navigation target
 */
export const calculateNextTarget = (
  currentTask: WorkflowStep,
  currentStep: WorkflowStep | null,
  maps: WorkflowMaps,
): NavigationTarget | null => {
  // For tasks without steps
  if (currentTask.children.length === 0) {
    const nextTask = findNextAvailableTask(currentTask.id, maps);
    if (!nextTask) {
      return null;
    }

    return nextTask.children.length > 0
      ? { taskId: nextTask.id, stepId: nextTask.children[0]?.id }
      : { taskId: nextTask.id };
  }

  // For tasks with steps
  if (currentStep) {
    const stepInfo = maps.stepMap.get(currentStep.id);
    if (!stepInfo) {
      return null;
    }

    // Check if there's a next step in current task
    if (stepInfo.stepIndex < currentTask.children.length - 1) {
      const nextStep = currentTask.children[stepInfo.stepIndex + 1];
      return nextStep ? { taskId: currentTask.id, stepId: nextStep.id } : null;
    }

    // Move to next task
    const nextTask = findNextAvailableTask(currentTask.id, maps);
    if (!nextTask) {
      return null;
    }

    return nextTask.children.length > 0
      ? { taskId: nextTask.id, stepId: nextTask.children[0]?.id }
      : { taskId: nextTask.id };
  }

  return null;
};

/**
 * Calculates previous navigation target
 */
export const calculatePreviousTarget = (
  currentTask: WorkflowStep,
  currentStep: WorkflowStep | null,
  maps: WorkflowMaps,
): NavigationTarget | null => {
  // Try to move to previous step within current task
  if (currentStep) {
    const stepInfo = maps.stepMap.get(currentStep.id);
    if (stepInfo && stepInfo.stepIndex > 0) {
      const prevStep = currentTask.children[stepInfo.stepIndex - 1];
      return prevStep ? { taskId: currentTask.id, stepId: prevStep.id } : null;
    }
  }

  // Move to previous task
  const prevTask = findPreviousTask(currentTask.id, maps);
  if (!prevTask) {
    return null;
  }

  if (prevTask.children.length > 0) {
    const lastStep = prevTask.children[prevTask.children.length - 1];
    return lastStep ? { taskId: prevTask.id, stepId: lastStep.id } : null;
  }

  return { taskId: prevTask.id };
};

/**
 * Finds the first active task and step
 */
export const findFirstActiveTaskAndStep = (workflowSteps: WorkflowStep[]) => {
  const firstTask = workflowSteps.find(
    // task => task.status === EStatusTask.PENDING || task.status === EStatusTask.IN_PROGRESS,
    (_task, index) => index === 0,
  );

  if (!firstTask) {
    return { task: null, step: null };
  }

  const firstStep = firstTask.children.find(
    // step => step.status === EStatusTask.PENDING || step.status === EStatusTask.IN_PROGRESS,
    (_step, index) => index === 0,
  );

  return { task: firstTask, step: firstStep || firstTask || null };
};

/**
 * Immutably updates a task in the workflow
 */
export const updateTaskInWorkflow = (
  workflow: WorkflowStep[],
  taskId: string,
  updater: (task: WorkflowStep, index: number) => WorkflowStep,
): WorkflowStep[] => {
  const taskIndex = workflow.findIndex(t => t.id === taskId);
  if (taskIndex === -1 || !workflow[taskIndex]) {
    return workflow;
  }

  const newWorkflow = [...workflow];
  newWorkflow[taskIndex] = updater(workflow[taskIndex], taskIndex);
  return newWorkflow;
};

/**
 * Immutably updates a step within a task
 */
export const updateStepInTask = (
  task: WorkflowStep,
  stepId: string,
  updater: (step: WorkflowStep, index: number) => WorkflowStep,
): WorkflowStep => {
  const stepIndex = task.children.findIndex(s => s.id === stepId);
  if (stepIndex === -1 || !(task.children && task.children[stepIndex])) {
    return task;
  }

  const newSteps = [...task.children];
  newSteps[stepIndex] = updater(task.children[stepIndex], stepIndex);

  return { ...task, children: newSteps };
};

/**
 * Auto-starts the first pending task
 */
export const autoStartFirstTask = (workflow: WorkflowStep[]): WorkflowStep[] => {
  const firstPendingTask = workflow.find(task => task.status === EStatusTask.PENDING);
  if (!firstPendingTask) {
    return workflow;
  }

  return updateTaskInWorkflow(workflow, firstPendingTask.id, (task) => {
    const updatedTask = { ...task };

    if (task.children.length > 0 && task.children[0]) {
      updatedTask.children = task.children.map((step, index) =>
        index === 0 ? { ...step } : step,
      );
    }

    return updatedTask;
  });
};

/**
 * Navigation cache with functional approach
 */
export const createNavigationCache = () => {
  const cache = new Map<string, NavigationTarget | null>();

  const getCacheKey = (direction: 'next' | 'prev', taskId: string, stepId?: string): string =>
    `${direction}_${taskId}_${stepId || 'no-step'}`;

  const get = (direction: 'next' | 'prev', taskId: string, stepId?: string) =>
    cache.get(getCacheKey(direction, taskId, stepId));

  const set = (direction: 'next' | 'prev', taskId: string, stepId: string | undefined, value: NavigationTarget | null) =>
    cache.set(getCacheKey(direction, taskId, stepId), value);

  const clear = () => cache.clear();

  return { get, set, clear };
};

export const transformProjectStepsToHierarchy = (workflowSteps: WorkflowStep[]): WorkflowStep[] => {
  const nodeMap = new Map(); // O(1) access for nodes
  const result = [];

  // Step 1: Create a map of all nodes
  for (const item of workflowSteps) {
    nodeMap.set(item.id, {
      ...item,
      status: !item.status ? EStatusTask.PENDING : EStatusTask.COMPLETED,
      steps: [],
    });
  }

  // Step 2: Assign children to their parent
  for (const item of workflowSteps) {
    if (item.parent) {
      const parentNode = nodeMap.get(item.parent);
      const childNode = nodeMap.get(item.id);
      if (parentNode) {
        parentNode.steps.push(childNode);
      }
    } else {
      result.push(nodeMap.get(item.id));
    }
  }

  // Step 3: Sort children (steps) and top-level nodes by 'order'
  const sortSteps = (steps: WorkflowStep[]) => {
    steps.sort((a, b) => a.order - b.order);
    for (const step of steps) {
      if (step.children?.length) {
        sortSteps(step.children);
      }
    }
  };

  sortSteps(result);

  return result;
};

export function processEvaluationData(data: any, output: QAStrategistOutput) {
  const {
    initialEvaluationData,
    financialCapacitySection,
    collaborationSection,
    growthPotentialSection,
  } = mapEvaluationDataStore(data, output);

  const updatedState = {
    [SectionType.CLIENT_PROFILE]: [...initialEvaluationData],
    [SectionType.FINANCIAL_CAPACITY]: [...financialCapacitySection],
    [SectionType.COLLABORATION]: [...collaborationSection],
    [SectionType.GROWTH_POTENTIAL]: [...growthPotentialSection],
  };

  const sectionTotals = {} as SectionTotals;
  let totalWeight = 0;
  let totalScore = 0;

  Object.values(SectionType).forEach((sectionType) => {
    const sectionData = updatedState[sectionType];
    const sectionWeight = calculateSectionWeight(sectionData);
    const sectionScore = calculateSectionScore(sectionData);

    sectionTotals[sectionType] = {
      weight: sectionWeight,
      score: sectionScore,
    };

    totalWeight += Number.parseFloat(sectionWeight);
    totalScore += Number.parseFloat(sectionScore);
  });

  sectionTotals.total = {
    weight: totalWeight.toFixed(2),
    score: totalScore.toFixed(2),
  };

  const normalizedScore = totalScore;
  const percentile = Math.min(normalizedScore * 20, 100);
  const rank = getRank(normalizedScore);

  const overallScore = {
    score: normalizedScore,
    percentile,
    rank,
    data: {
      [SectionType.CLIENT_PROFILE]: JSON.parse(JSON.stringify(updatedState[SectionType.CLIENT_PROFILE])),
      [SectionType.FINANCIAL_CAPACITY]: JSON.parse(JSON.stringify(updatedState[SectionType.FINANCIAL_CAPACITY])),
      [SectionType.COLLABORATION]: JSON.parse(JSON.stringify(updatedState[SectionType.COLLABORATION])),
      [SectionType.GROWTH_POTENTIAL]: JSON.parse(JSON.stringify(updatedState[SectionType.GROWTH_POTENTIAL])),
    },
  };

  return { updatedState, sectionTotals, overallScore };
}

export function updateNameFieldForQuestionnaire(questionnaire: QuestionnaireResponse) {
  const sectionConvert = questionnaire.sections.map((section, index) => ({
    ...section,
    questions: handleAddFieldNameInData(section, index),
  }));
  const questionnaireConvert = {
    ...questionnaire,
    sections: [...sectionConvert],
  };

  return questionnaireConvert;
}
