import { http } from '@/core/http/http';
import type { FrameworkPayload, FrameworkResponseType, TemplatePayload, TemplateResponse } from '../../types';
import type { ApiResponse } from '@/shared/types/api-response';

/**
 * Creates a new template
 */
export async function createTemplateFramework(data: TemplatePayload): Promise<ApiResponse<TemplateResponse>> {
  return await http.post<TemplateResponse>({
    url: '/templates/framework',
    data,
  });
}

/**
 * Creates a new Framework
 */
export async function createFramework(data: FrameworkPayload): Promise<ApiResponse<boolean>> {
  return await http.post<boolean>({
    url: '/frameworks',
    data,
  });
}

export async function getFrameworkList(): Promise<ApiResponse<FrameworkResponseType>> {
  return await http.get<FrameworkResponseType>({ url: `/frameworks` });
}
