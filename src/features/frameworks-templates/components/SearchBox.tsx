'use client';

import Input from '@/shared/components/form/input/InputField';
import FancyLoader from '@/shared/components/ui/fancy-loader/FancyLoader';
import { MagnifyingGlassIcon } from '@/shared/icons';
import React, { useState } from 'react';

type SearchBoxType = {
  placeholder?: string;
};

export function SearchBox({ placeholder }: SearchBoxType) {
  const [isLoading, _setIsLoading] = useState<boolean>(false);

  const [value, setValue] = useState<string>('');

  const handleSearchChange = React.useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
  }, []);

  return (
    <div className="flex-grow text-right relative">
      <Input
        type="text"
        id="search"
        placeholder={placeholder}
        value={value}
        onChange={handleSearchChange}
        className="pr-7"
      />

      <MagnifyingGlassIcon className="h-5 w-5 absolute right-2 top-1/2 transform -translate-y-1/2" />

      {isLoading && (
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
          <FancyLoader />
        </div>
      )}
    </div>
  );
}
