'use client';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import TableTemplates from '../table-list/TableTemplate';
import type { TableConfig } from '../table-list/TableTemplate';
import { useCallback, useRef, useState } from 'react';
import { useTemplateCreate } from '@/features/frameworks-templates/hooks/useTemplateCreate';
import { toast } from 'sonner';
import { useFrameworkCreate } from '@/features/frameworks-templates/hooks/useFrameworkCreate';
import type { TemplatePayload } from '@/features/frameworks-templates/types';

type TemplateFormType = {
  isOpen: boolean;
  closeModal: () => void;
};

export type TemplateDataTypeRef = {
  onGetTemplate: () => any[];
};

const TemplateForm: React.FC<TemplateFormType> = ({ isOpen, closeModal }) => {
  const [researchNameType, setResearchNameType] = useState<string>('');

  const templateListRef = useRef<TemplateDataTypeRef | null>(null);

  const { mutateAsync } = useTemplateCreate();

  const { mutateAsync: createFramework } = useFrameworkCreate();

  const templateList: any[] = [];

  const tableConfig: TableConfig[] = [
    {
      tag: 'template',
      label: 'Template',
      type: 'text',
    },
    // {
    //   tag: 'step',
    //   label: 'Step',
    // },
    {
      tag: 'file',
      label: 'Questionnaire',
      type: 'file',
    },
    {
      tag: 'fileReport',
      label: 'Report',
      type: 'file',
    },
    {
      tag: 'actions',
      label: '',
      type: 'action',
    },
  ];

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setResearchNameType(e.target.value);
  }, []);

  const handleSubmit = async () => {
    const templateList = templateListRef.current?.onGetTemplate() ?? [];

    if (!researchNameType) {
      toast.error('Framework name cannot be empty');
      return;
    }

    const payloads: TemplatePayload[] = templateList.map((template) => {
      const payload = { types: [
        template.file.name && {
          [template.file.name]: template.file.type,
          category: 'questionnaire',
        },
        template.fileReport.name && {
          [template.fileReport.name]: template.fileReport.type,
          category: 'report',
        },
      ], fileIds: [
        template.fileReport.name
        && template.fileReport._id,

        template.file.name
        && template.file._id,

      ], type: 'framework', name: template.template };

      return payload;
    });

    console.log(payloads);

    try {
      const response = await Promise.all(payloads.map(payload => mutateAsync(payload)));
      const payload = {
        name: researchNameType,
        templateIds: response.map(res => res.data?.id ?? ''),
      };
      try {
        const res = await createFramework(payload);
        toast.success(res.message);
        closeModal();
      } catch (error: any) {
        toast.error(error.message);
        console.log(error);
      }
      console.log(response);
    } catch (error) {
      console.error(error);
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={closeModal} className="max-w-[900px] min-w-[890px]">
      <div className="no-scrollbar w-full p-6">
        <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
          Create framework
        </h4>
        <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
          Please enter all the required information to proceed.
        </p>

        <div>
          <div>
            <Label htmlFor="name">
              Research type
              {' '}
              <span className="text-error-500">*</span>
            </Label>

            <Input
              id="name"
              placeholder="Research type"
              type="text"
              value={researchNameType}
              onChange={handleSearchChange}
            />
          </div>

          <div className="mt-3">
            <Label htmlFor="template">
              Template files upload
            </Label>

            <div className="max-h-100 overflow-auto border-b relative">
              <TableTemplates
                ref={templateListRef}
                templateList={templateList}
                tableConfig={tableConfig}
                backgroundColorClass="bg-white"
                isForm={true}
              />
            </div>
          </div>

        </div>

        <div className="flex items-center justify-end gap-4 mt-3">
          <Button
            type="button"
            variant="outline"
            onClick={closeModal}
          >
            Cancel
          </Button>

          <Button
            type="button"
            onClick={handleSubmit}
          >
            Create
          </Button>
        </div>
      </div>

    </Modal>
  );
};

export default TemplateForm;
