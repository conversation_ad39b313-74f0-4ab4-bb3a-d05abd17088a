'use client';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shared/components/ui/table';
import React, { useImperativeHandle, useState } from 'react';
import { SkeletonTable } from './SkeletonTable';
import { ArrowUpTrayIcon, CheckIcon, FileEditIcon, LinkIcon, PlusIcon, TrashIcon, XIcon } from '@/shared/icons';
import Link from 'next/link';
import Label from '@/shared/components/form/Label';
import Input from '@/shared/components/form/input/InputField';
import Select from '@/shared/components/form/Select';
import type { TemplateDataTypeRef } from '../modals/TemplatesForm';
import { toast } from 'sonner';
import { http } from '@/core/http/http';
import type { IFileResponse } from '@/shared/types/global';
import { Env } from '@/core/config/Env';

export type TableConfig = {
  tag: string;
  label: string;
  className?: string;
  type: 'file' | 'dropdown' | 'text' | 'no-view' | 'action';
};

export type TableTemplateType = {
  templateList: any[];
  tableConfig: TableConfig[];
  backgroundColorClass?: string;
  isForm?: boolean;
  ref?: React.Ref<TemplateDataTypeRef>;
  onSaveTemplate?: (index: number, item: any) => void;
};

const TableTemplates: React.FC<TableTemplateType> = ({
  templateList,
  tableConfig,
  backgroundColorClass = 'bg-gray-100',
  isForm,
  ref,
  onSaveTemplate,
}) => {
  const stepList = [{
    value: '1',
    label: 'Step 1',
  }, {
    value: '2',
    label: 'Step 2',
  }, {
    value: '5',
    label: 'Step 5',
  }];

  const [isLoading, _setIsLoading] = useState<boolean>(false);

  const [templateListData, setTemplateListData] = useState<any[]>(() => templateList);

  const handleChangeSelection = () => { };

  const handleChangeText = (e: React.ChangeEvent<HTMLInputElement>, item: any, tag: string, index: number) => {
    const text = e.target.value;
    item[tag] = text;
    const templateList = [...templateListData];
    templateList[index] = { ...item };

    setTemplateListData(templateList);
  };

  const handleAddTemplate = () => {
    const initData = {
      template: '',
      step: '1',
      file: {
        url: '',
        name: '',
      },
      fileReport: {
        url: '',
        name: '',
      },
      status: 'new',
      id: `new-${templateListData.length}`,
    };

    setTemplateListData(prev => [...prev, initData]);
  };

  const getFileResponse = async (file: File) => {
    console.log(file);
    const formData = new FormData();
    formData.append('file', file);

    try {
      const uploadResponse = http.post<IFileResponse>({
        url: '/files/upload',
        data: formData,
        options: {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
      });

      const res = await uploadResponse;
      toast.success('File is uploaded success');

      return res;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const handleUploadFile = async (e: React.ChangeEvent<HTMLInputElement>, item: any, tag: string) => {
    const file = e.target.files?.[0] as File;
    const { data } = await getFileResponse(file);
    const templateList = [...templateListData];
    const index = templateListData.findIndex(t => t.id === item.id);
    if (index !== -1) {
      const itemSelected = templateList[index];
      itemSelected[tag] = { ...data, name: data?.originalname, url: `${Env.NEXT_PUBLIC_API_SERVER}/public/${data?.key}`, type: file?.type,
      };

      templateList[index] = { ...itemSelected };

      setTemplateListData(templateList);
    }
  };

  const handleSaveTemplate = (index: number, item: any) => {
    const templateList = [...templateListData];

    if (!item.template) {
      toast.error('Template name cannot be empty');
      return;
    }
    if (!item.fileReport?.name && !item.file?.name) {
      toast.error('Please upload at least one file for the questionnaire or report.');
      return;
    }

    if (onSaveTemplate) {
      onSaveTemplate(index, item);
      return;
    }
    item.status = 'old';

    templateList[index] = { ...item };

    setTemplateListData(templateList);
  };

  useImperativeHandle(ref, () => {
    return {
      onGetTemplate: () => {
        return templateListData.filter(t => t.status === 'old');
      },
    };
  }, [templateListData]);

  return (
    <Table className="border border-solid rounded-xl max-h-100">
      <TableHeader className="bg-gray-300 rounded-t-xl">
        <TableRow>
          {tableConfig.map(config => (
            <TableHead key={config.tag} className={config?.className}>
              {config.label}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody className={`${backgroundColorClass} max-h-[400px]`}>
        {isLoading
          ? <SkeletonTable tableConfig={tableConfig} />
          : templateListData.map((item, index) => (
              <React.Fragment key={item.id || index}>
                {item.status === 'old'
                  ? (
                      <TableRow>
                        {tableConfig.map((config, i) => (
                          config.type === 'file'
                            ? (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  {(item?.isEdit || isForm)
                                    ? (
                                        <div className="flex items-center gap-2 ">
                                          <Link href={item[config.tag]?.url}>

                                            <p className={`underline truncate ${isForm && 'max-w-30'} mb-0 `}>{item[config.tag].name}</p>
                                          </Link>

                                          <Label className="mb-0 text-black" htmlFor={`upload-${item.id}-${config.tag}`}>
                                            <ArrowUpTrayIcon className="h-5 w-5 cursor-pointer" />
                                          </Label>

                                          <Input
                                            id={`upload-${item.id}-${config.tag}`}
                                            type="file"
                                            onChange={e => handleUploadFile(e, item, config.tag)}
                                            className="hidden"
                                          />
                                        </div>

                                      )
                                    : (
                                        <div className="flex items-center gap-2">
                                          <LinkIcon className="h-5 w-5" />
                                          <p className="m-0 underline cursor-pointer">View file</p>
                                        </div>
                                      )}
                                </TableCell>
                              )
                            : config.type !== 'action'
                              ? (
                                  <TableCell key={config.tag + i} className={config?.className}>
                                    {!item?.isEdit
                                      ? (
                                          <div className={`pl-2 shrink-0 w-full ${isForm && 'min-w-80'}`}>
                                            {item[config.tag]}
                                          </div>
                                        )
                                      : (
                                          config.tag === 'step'
                                            ? (
                                                <Select
                                                  options={stepList}
                                                  onChange={handleChangeSelection}
                                                  defaultValue={item[config.tag] || ''}
                                                  isHiddenPlaceHolder={true}
                                                />
                                              )
                                            : (config.tag === 'template'
                                                ? (
                                                    <Input
                                                      id={`input-${item.id}-${config.tag}`}
                                                      type="text"
                                                      onChange={
                                                        e => handleChangeText(e, item, config.tag, index)
                                                      }
                                                      defaultValue={item[config.tag]}
                                                      className={`bg-white shrink-0 w-full ${isForm && 'min-w-80'}`}
                                                    />
                                                  )
                                                : item[config.tag]
                                              )
                                        )}
                                  </TableCell>
                                )

                              : (
                                  <TableCell key={config.tag + i} className={config?.className}>
                                    {!item?.isEdit
                                      ? (
                                          <div className="flex items-center gap-2 justify-end">
                                            <TrashIcon className="h-5 w-5 cursor-pointer text-red-500" />
                                            {!isForm && <FileEditIcon onClick={() => item.isShowMore = !item.isShowMore} className="h-5 w-5 cursor-pointer" />}
                                          </div>
                                        )
                                      : (
                                          <div className="flex items-center gap-2 justify-end">
                                            <CheckIcon className="h-5 w-5 cursor-pointer text-green-500" />
                                            <XIcon className="h-5 w-5 cursor-pointer text-red-500" />
                                          </div>
                                        )}
                                  </TableCell>
                                )
                        ))}
                      </TableRow>
                    )
                  : (
                      <TableRow>
                        {tableConfig.map((config, i) => (
                          config.type === 'file'
                            ? (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  {(item[config.tag].name)
                                    ? (
                                        <div className="flex items-center gap-2 ">
                                          <Link href={item[config.tag].url}>

                                            <p className={`underline truncate ${isForm && 'max-w-30'} mb-0 `}>{item[config.tag].name}</p>
                                          </Link>

                                          <Label className="mb-0 text-black" htmlFor={`upload-${item.id}-${config.tag}`}>
                                            <ArrowUpTrayIcon className="h-5 w-5 cursor-pointer" />
                                          </Label>

                                          <Input
                                            id={`upload-${item.id}-${config.tag}`}
                                            type="file"
                                            onChange={e => handleUploadFile(e, item, config.tag)}
                                            className="hidden"
                                          />
                                        </div>

                                      )
                                    : (
                                        <div className="flex items-center gap-2">
                                          <p className="m-0 underline cursor-pointer">Browse files</p>

                                          <Label className="mb-0 text-black" htmlFor={`upload-new-${item.id}-${config.tag}`}>
                                            <ArrowUpTrayIcon className="h-5 w-5 cursor-pointer" />
                                          </Label>

                                          <Input
                                            id={`upload-new-${item.id}-${config.tag}`}
                                            type="file"
                                            onChange={e =>
                                              handleUploadFile(e, item, config.tag)}
                                            className="hidden"
                                          />
                                        </div>
                                      )}
                                </TableCell>
                              )
                            : config.type !== 'action'
                              ? (
                                  <TableCell key={config.tag + i} className={config?.className}>
                                    {
                                      config.tag === 'step'
                                        ? (
                                            <Select
                                              options={stepList}
                                              onChange={handleChangeSelection}
                                              defaultValue={item[config.tag] || ''}
                                              isHiddenPlaceHolder={true}
                                            />
                                          )
                                        : (config.tag === 'template'
                                            ? (
                                                <Input
                                                  id={`input-${item.id}-${config.tag}`}
                                                  type="text"
                                                  onChange={e => handleChangeText(e, item, config.tag, index)}
                                                  defaultValue={item[config.tag]}
                                                  className={`bg-white shrink-0 w-full ${isForm && 'min-w-80'}`}
                                                />
                                              )
                                            : item[config.tag]

                                          )
                                    }
                                  </TableCell>
                                )

                              : (
                                  <TableCell key={config.tag + i} className={config?.className}>

                                    <div className="flex items-center gap-2 justify-end">
                                      <CheckIcon onClick={() => handleSaveTemplate(index, item)} className="h-5 w-5 cursor-pointer text-green-500" />
                                      <XIcon className="h-5 w-5 cursor-pointer text-red-500" />
                                    </div>

                                  </TableCell>
                                )
                        ))}
                      </TableRow>
                    )}

              </React.Fragment>
            ))}
        {isForm && (
          <TableRow className="sticky bottom-0" onClick={handleAddTemplate}>
            <TableCell colSpan={tableConfig.length} className="cursor-pointer bg-gray-200">
              <div className="flex items-center justify-center">
                <PlusIcon className="w-5 h-5" />
                Add
              </div>
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

export default TableTemplates;
