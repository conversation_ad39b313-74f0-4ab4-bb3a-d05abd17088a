'use client';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shared/components/ui/table';
import React, { useEffect, useState } from 'react';
import { SkeletonTable } from './SkeletonTable';
import { ChevronDownIcon, PlusCircleIcon, ShowMoreIcon } from '@/shared/icons';
import TableTemplates from './TableTemplate';
import type { ItemFrameworkResponse, TemplateItemFramework } from '@/features/frameworks-templates/types';
import { useFrameworkGet } from '@/features/frameworks-templates/hooks/useFrameworkGet';
import { dateToDDMMYYHHMM } from '@/shared/utils/date';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';

export type TableConfig = {
  tag: string;
  label: string;
  className?: string;
  type: 'file' | 'dropdown' | 'text' | 'no-view' | 'action';
};

type TemplateList = {
  id: string;
  template: string;
  isShowMore: boolean;
  isEdit: boolean;
  file: any;
  reportFile: any;
  status: string;
};

type FrameworkTableData = {
  id: string;
  no: number;
  framework: string;
  updatedAt: string;
  isShowMore: boolean;
  templateList: TemplateList[];
  [key: string]: string | boolean | number | TemplateList[];
};

const TableManagement: React.FC = () => {
  const { data, isLoading } = useFrameworkGet();

  const tableConfig: TableConfig[] = [
    {
      tag: 'no',
      label: 'No.',
      type: 'no-view',
    },
    {
      tag: 'framework',
      label: 'Research Type',
      type: 'text',
    },
    {
      tag: 'updatedAt',
      label: 'Last updated',
      type: 'text',
    },
    {
      tag: 'actions',
      label: '',
      className: 'w-24',
      type: 'action',
    },
  ];

  const tableConfigTemplate: TableConfig[] = [
    {
      tag: 'no',
      label: '',
      type: 'no-view',
    },
    {
      tag: 'template',
      label: 'Template',
      type: 'no-view',
    },
    // {
    //   tag: 'step',
    //   label: 'Step',
    // },
    {
      tag: 'file',
      label: 'Questionnaire',
      type: 'file',
    },
    {
      tag: 'reportFile',
      label: 'Report',
      type: 'file',
    },
    {
      tag: 'actions',
      label: '',
      className: 'w-24',
      type: 'action',
    },
  ];

  const [frameworkList, setIsFrameworkList] = useState<FrameworkTableData[]>([]);

  const toggleShowMore = (itemId: string) => {
    setIsFrameworkList(prevList =>
      prevList.map(item =>
        item.id === itemId
          ? { ...item, isShowMore: !item.isShowMore }
          : item,
      ),
    );
  };

  const initTemplateList = (templates: TemplateItemFramework[]) => {
    return templates.map((template) => {
      const file = template.files.find(f => f.category === 'questionnaire');
      const reportFile = template.files.find(f => f.category === 'report');
      return ({
        id: template.id,
        template: template.name,
        isShowMore: false,
        isEdit: false,
        status: 'old',
        file: {
          ...file?.file,
          url: file ? file.file.url : '',
          name: file ? file.file.originalname : '',
        },
        reportFile: {
          ...reportFile?.file,
          url: reportFile ? reportFile.file.url : '',
          name: reportFile ? reportFile.file.originalname : '',
        },
      });
    });
  };

  const initDataTable = (data: ItemFrameworkResponse[]) => {
    return data.map((item, index) => ({
      no: index + 1,
      framework: item.name,
      updatedAt: dateToDDMMYYHHMM(item.updatedAt),
      isShowMore: false,
      id: item.id,
      templateList: initTemplateList(item.templates),
    }));
  };

  const handleOpenModal = () => {

  };

  useEffect(() => {
    if (data) {
      const { items } = data;
      const frameworkList = initDataTable(items);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsFrameworkList(frameworkList);
    }
  }, [data]);

  return (
    <Table className="border border-solid rounded-xl">
      <TableHeader className="bg-gray-50 rounded-t-xl">
        <TableRow>
          {tableConfig.map(config => (
            <TableHead key={config.tag} className={config?.className}>
              {config.label}
            </TableHead>
          ))}
        </TableRow>
      </TableHeader>
      <TableBody>
        {isLoading
          ? <SkeletonTable tableConfig={tableConfig} />
          : frameworkList.length
            ? (
                frameworkList.map((item, index) => (
                  <React.Fragment key={item.id || index}>
                    <TableRow>
                      {tableConfig.map((config, i) => (
                        config.tag !== 'actions'
                          ? (
                              <TableCell key={config.tag + i} className={config?.className}>
                                {item[config.tag] as (string | number)}
                              </TableCell>
                            )
                          : (
                              <TableCell key={config.tag + i} className={config?.className}>
                                <div className="flex items-center gap-2">
                                  <Popover open={item.} onOpenChange={setIsPopoverOpen}>
                                    <PopoverTrigger>
                                      <ShowMoreIcon className="h-5 w-5 cursor-pointer" />
                                    </PopoverTrigger>
                                    <PopoverContent className="w-35 p-2">
                                      <div className="">
                                        <div className="p-1 cursor-pointer text-sm hover:bg-gray-50">Edit</div>
                                        <div onClick={handleOpenModal} className="p-1 cursor-pointer text-sm text-red-500 hover:bg-gray-100">Delete</div>
                                      </div>
                                    </PopoverContent>
                                  </Popover>
                                  <ChevronDownIcon onClick={() => toggleShowMore(item.id)} className="h-5 w-5 cursor-pointer" />
                                </div>
                              </TableCell>
                            )
                      ))}

                    </TableRow>
                    {
                      item.isShowMore && (
                        <TableRow>
                          <TableCell colSpan={tableConfig.length}>
                            <div className="w-full">
                              <TableTemplates tableConfig={tableConfigTemplate} templateList={item.templateList} />
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    }
                  </React.Fragment>
                ))
              )
            : (
                <TableRow>
                  <TableCell className="bg-gray-50" colSpan={tableConfig.length}>
                    <div className="min-h-[202px] w-full bg-gray-50 flex items-center justify-center">
                      <div className="cursor-pointer">
                        <div className="flex flex-col items-center justify-center gap-2">
                          <PlusCircleIcon className="h-6 w-6" />
                          <h3 className="text-lg">Create new</h3>
                        </div>
                      </div>
                    </div>
                  </TableCell>
                </TableRow>
              )}
      </TableBody>
    </Table>
  );
};

export default TableManagement;
