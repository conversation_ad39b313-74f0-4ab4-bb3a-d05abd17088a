import { remark } from 'remark';
import remarkHtml from 'remark-html';

import rehypeParse from 'rehype-parse';
import rehypeRemark from 'rehype-remark';
import remarkStringify from 'remark-stringify';

import TurndownService from 'turndown';
import { marked } from 'marked';

export function markdownToHtml(markdownText: string) {
  const file = remark().use(remarkHtml).processSync(markdownText);
  return String(file);
}

export function htmlToMarkdown(htmlText: string) {
  const file = remark()
    .use(rehypeParse, { emitParseErrors: true, duplicateAttribute: false })
    .use(rehypeRemark)
    .use(remarkStringify, {
      bullet: '-',
      fences: true,
      listItemIndent: 'one',
    })
    .processSync(htmlText);

  return String(file);
}

export function markdownToHtmlVer2(markdownText: string) {
  marked.setOptions({
    gfm: true,
    breaks: true,
  });

  return marked(markdownText);
}

export function htmlToMarkdownVer2(htmlText: string) {
  const turndownService = new TurndownService();
  return turndownService.turndown(htmlText);
}
